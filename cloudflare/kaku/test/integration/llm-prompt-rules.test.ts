import { env } from 'cloudflare:test';
import { beforeAll, describe, expect, it } from 'vitest';
import { PageStateResult } from '../../src/agent/types/extract-result';
import { FormVisionResult } from '../../src/form-generation/types/form-interfaces';
import { GeminiLLMRepository } from '../../src/llm/GeminiLLMRepository';
import { PlatformTypes } from '../../src/ui/constants';
import { convertToPageStateResult, parseFormVisionResult } from '../../src/workflow/utils/helpers';
import { FORM_VISION_PROMPT_V6 } from '../scripts/geminiPerformance';

const GEMINI_API_KEY = 'AIzaSyDzh7xuznZzDi0c0DCYegXqGDH3UxFF3DQ'; //TODO(Add API key here to run this script)
const GEMINI_BASE_URL =
  'https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/google-ai-studio';

const DEFAULT_TEST_ITERATIONS = 10; // Number of iterations for each test

const TEST_TIMEOUT = 60000; // 60s - 6s for each of the 10 iterations

const imageBaseURL = env.KAKU_API_ENDPOINT;

async function fetchScreenshot(fileName: string): Promise<string> {
  try {
    const url = `${imageBaseURL}/${fileName}`;
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch ${fileName}: ${response.statusText}`);
    }
    const arrayBuffer = await response.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    return buffer.toString('base64');
  } catch (error) {
    console.error(`Failed to fetch screenshot ${fileName}:`, error);
    throw error;
  }
}

describe.skip('LLM Prompt Rules Test', () => {
  let geminiRepository: GeminiLLMRepository;

  beforeAll(() => {
    if (!GEMINI_API_KEY) {
      console.error('Gemini API key is missing.');
      throw new Error('Gemini API key is missing.');
    }
    geminiRepository = new GeminiLLMRepository(GEMINI_API_KEY, GEMINI_BASE_URL);
    console.log('GEMINI Repository initialized');
  });

  const runTest = async (
    testName: string,
    screenshotPath: string,
    platform: PlatformTypes,
    validate: (result: PageStateResult, formVisionResult: FormVisionResult) => void,
  ): Promise<void> => {
    const screenshot = await fetchScreenshot(screenshotPath);

    for (let i = 1; i <= DEFAULT_TEST_ITERATIONS; i++) {
      const geminiResponse = await geminiRepository.getLLMResponse({
        platform,
        prompt: FORM_VISION_PROMPT_V6,
        screenshot,
        skipCache: true,
        viewportWidth: 800,
        viewportHeight: 600,
      });

      let formVisionResult: FormVisionResult;

      try {
        formVisionResult = parseFormVisionResult(geminiResponse.output_text);
      } catch (error) {
        throw new Error(`Invalid JSON response from form vision analysis: ${error}`);
      }

      const result = convertToPageStateResult(formVisionResult);

      validate(result, formVisionResult);
    }
  };

  // Critical Rule: Return valid JSON format
  it(
    'should return the valid required JSON format',
    async () => {
      await runTest(
        'Return valid JSON format',
        'test/files/screenshots/google_login.webp',
        'google',
        (result) => {
          expect(typeof result).toBe('object');
          expect(result).toHaveProperty('formTitle');
          expect(result).toHaveProperty('formDescription');
          expect(result).toHaveProperty('errors');
          expect(result).toHaveProperty('pageType');
          expect(result).toHaveProperty('htmxForm');
          expect(result).toHaveProperty('actions');
          expect(() => JSON.stringify(result)).not.toThrow();
        },
      );
    },
    TEST_TIMEOUT,
  );

  // Critical Rule: Ensure input fields are empty
  it(
    'should ensure input fields are empty',
    async () => {
      await runTest(
        'Ensure empty input fields',
        'test/files/screenshots/google_login.webp',
        'google',
        (result) => {
          const inputs = result.htmxForm.match(/<input[^>]*>/g) || [];
          inputs.forEach((input) => {
            const hasEmptyValue =
              input.includes('value=""') || input.includes("value=''") || !input.includes('value');
            expect(hasEmptyValue, `Input field is not empty: ${input}`).toBe(true);
          });
        },
      );
    },
    TEST_TIMEOUT,
  );

  // Error Message Detection: Detect error messages
  it(
    'should detect error messages',
    async () => {
      await runTest(
        'Error messages detection',
        'test/files/screenshots/google_wrong_password.png',
        'google',
        (result) => {
          expect(Array.isArray(result.errors)).toBe(true);
          expect(result.errors.length).toBeGreaterThan(0);

          expect(result.htmxForm).toContain('<div class="form-error">');
        },
      );
    },
    TEST_TIMEOUT,
  );

  // Exclusion Rule: Exclude "Forgot Password" or "Reset Password"
  it(
    "should exclude 'Forgot Password' or 'Reset Password'",
    async () => {
      await runTest(
        'Exclude Forgot Password or Reset Password',
        'test/files/screenshots/facebook_login.webp',
        'facebook',
        (result) => {
          const forgotPasswordDetectionRegex =
            /\b(?:forgot(?:ten)?|reset)(?:\s+your)?\s+password\b\??/i;

          expect(result.htmxForm.toLowerCase()).not.toMatch(forgotPasswordDetectionRegex);
          expect(
            result.actions.some((action) =>
              forgotPasswordDetectionRegex.test(action.name.toLowerCase()),
            ),
          ).toBe(false);
        },
      );
    },
    TEST_TIMEOUT,
  );

  // Exclusion Rule: Exclude "Create Account" or "Sign Up"
  it(
    "should exclude 'Create Account', 'Sign Up' or similar action",
    async () => {
      await runTest(
        'Exclude Create Account or Sign up',
        'test/files/screenshots/facebook_login.webp',
        'facebook',
        (result) => {
          const createAccountDetectionRegex =
            /\b(?:(?:create|register)\s+(?:(?:a|an|new)\s+)*(?:account|one)|sign\s+up|register\s+yourself)\b/i;

          expect(result.htmxForm.toLowerCase()).not.toMatch(createAccountDetectionRegex);
          expect(
            result.actions.some((action) =>
              createAccountDetectionRegex.test(action.name.toLowerCase()),
            ),
          ).toBe(false);
        },
      );
    },
    TEST_TIMEOUT,
  );

  // Exclusion Rule: Exclude "Remember me" or "Stay signed in" from the form
  it(
    "should exclude 'Remember me' or 'Stay signed in' from the form",
    async () => {
      await runTest(
        'Exclude Remember Me or Stay Signed In',
        'test/files/screenshots/google-acknowledge.webp',
        'google',
        (result) => {
          const rememberMeDetectionRegex =
            /\b(?:remember\s+me|(?:stay|keep\s+me)\s+(?:signed|logged)\s+in|(?:remember|trust)\s+(?:this\s+)?(?:device|computer|browser|pc)|(?:don't|do\s+not)\s+ask\s+again)\b/i;

          expect(result.htmxForm.toLowerCase()).not.toMatch(rememberMeDetectionRegex);
          expect(
            result.actions.some((action) =>
              rememberMeDetectionRegex.test(action.name.toLowerCase()),
            ),
          ).toBe(true);
        },
      );
    },
    TEST_TIMEOUT,
  );

  // 2FA Rule: Manual "Acknowledge" Button should be included
  it(
    'should include Acknowledge button',
    async () => {
      await runTest(
        "2FA: Manual 'Acknowledge' Button",
        'test/files/screenshots/google-acknowledge.webp',
        'google',
        (result) => {
          const hasAcknowledgeButton =
            result.htmxForm.toLowerCase().includes('acknowledge') ||
            result.htmxForm.toLowerCase().includes('acknowledged');
          const hasAcknowledgeAction = result.actions.some(
            (action) =>
              action.name.toLowerCase().includes('acknowledge') ||
              action.name.toLowerCase().includes('acknowledged'),
          );

          expect(hasAcknowledgeButton).toBe(true);
          expect(hasAcknowledgeAction).toBe(true);
        },
      );
    },
    TEST_TIMEOUT,
  );

  // 2FA Rule: Should not include a manual "Acknowledge" Button if a submit/confirm button exists
  it(
    'should not include Acknowledge button if a submit/confirm button exists',
    async () => {
      await runTest(
        '2FA: No Acknowledge Button if Submit/Confirm Exists',
        'test/files/screenshots/github-2fa-code.png',
        'github',
        (result) => {
          const hasAcknowledgeButton =
            result.htmxForm.toLowerCase().includes('acknowledge') ||
            result.htmxForm.toLowerCase().includes('acknowledged');
          const hasAcknowledgeAction = result.actions.some(
            (action) =>
              action.name.toLowerCase().includes('acknowledge') ||
              action.name.toLowerCase().includes('acknowledged'),
          );

          expect(hasAcknowledgeButton).toBe(false);
          expect(hasAcknowledgeAction).toBe(false);
        },
      );
    },
    TEST_TIMEOUT,
  );

  // 2FA Rule: Highlight verification code
  it(
    'should highlight verification code in 2FA form',
    async () => {
      await runTest(
        '2FA: Highlight Verification Code',
        'test/files/screenshots/image-with-auth-code.webp',
        'google',
        (result, formVisionResult) => {
          expect(formVisionResult.metadata.promptCode).toBeDefined();

          expect(result.htmxForm.toLowerCase()).toContain('verification-code-display');
          expect(result.htmxForm.toLowerCase()).toContain('verification-code-value');
        },
      );
    },
    TEST_TIMEOUT,
  );

  // 2FA Rule: Remove "Trust this device" or "Remember Me" checkboxes
  it(
    "should remove 'Trust this device' or 'Remember Me' checkboxes in 2FA forms",
    async () => {
      await runTest(
        '2FA: Remove Checkbox from 2FA form',
        'test/files/screenshots/image-with-auth-code.webp',
        'google',
        (result) => {
          const rememberMeDetectionRegex =
            /\b(?:remember\s+me|(?:stay|keep\s+me)\s+(?:signed|logged)\s+in|(?:remember|trust)\s+(?:this\s+)?(?:device|computer|browser|pc)|(?:don't|do\s+not)\s+ask\s+again)\b/i;

          expect(result.htmxForm.toLowerCase()).not.toMatch(rememberMeDetectionRegex);
          expect(
            result.actions.some((action) =>
              rememberMeDetectionRegex.test(action.name.toLowerCase()),
            ),
          ).toBe(true);
        },
      );
    },
    TEST_TIMEOUT,
  );

  // Page Type Rule: Identify page type as 'authenticated'
  it(
    "should identify page as 'authenticated'",
    async () => {
      await runTest(
        'Page Type: authenticated',
        'test/files/screenshots/github_logged_in.webp',
        'github',
        (result) => {
          expect(result.pageType).toBe('authenticated');
        },
      );
    },
    TEST_TIMEOUT,
  );

  // Page Type Rule: Identify page type as 'not-authenticated'
  it(
    "should identify page as 'not-authenticated'",
    async () => {
      await runTest(
        'Page Type: not-authenticated',
        'test/files/screenshots/github_login.webp',
        'github',
        (result) => {
          expect(result.pageType).toBe('not-authenticated');
        },
      );
    },
    TEST_TIMEOUT,
  );

  // Page Type Rule: Identify page type as 'captcha'
  it(
    "should identify page type as 'captcha'",
    async () => {
      await runTest(
        'Page Type: captcha',
        'test/files/screenshots/kazeel_captcha.png',
        'kazeel',
        (result) => {
          expect(result.pageType).toBe('captcha');
        },
      );
    },
    TEST_TIMEOUT,
  );

  // Page Type Rule: Identify page type as 'loading'
  it(
    "should identify page type as 'loading'",
    async () => {
      await runTest(
        'Page Type: loading',
        'test/files/screenshots/github-loading.png',
        'github',
        (result) => {
          expect(result.pageType).toBe('loading');
        },
      );
    },
    TEST_TIMEOUT,
  );

  it(
    "should identify page type as 'authenticated' when the screenshot gives a hint that the user is already authenticated(case trust this device popup)",
    async () => {
      await runTest(
        'Page Type: authenticated',
        'test/files/screenshots/facebook_trust_this_device.png',
        'facebook',
        (result) => {
          expect(result.pageType).toBe('authenticated');
        },
      );
    },
    TEST_TIMEOUT,
  );

  // Page Type Rule: Half-completed state with title and sudden captcha marked as loading
  it(
    "should identify page type as 'loading' for half-completed state",
    async () => {
      await runTest(
        'Page Type: loading',
        'test/files/screenshots/fb-half-complete-state.png',
        'facebook',
        (result) => {
          expect(result.pageType).toBe('loading');
        },
      );
    },
    TEST_TIMEOUT,
  );
});
